/* Enhanced Design System for Forecaster */

/* Typography Scale */
:root {
  /* Font sizes */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */

  /* Line heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Spacing scale */
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  --space-24: 6rem;       /* 96px */

  /* Border radius */
  --radius-sm: 0.125rem;  /* 2px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Weather-specific colors */
  --weather-sunny: #f59e0b;
  --weather-cloudy: #6b7280;
  --weather-rainy: #3b82f6;
  --weather-stormy: #7c3aed;
  --weather-snowy: #e5e7eb;
  --weather-windy: #10b981;

  /* Alert colors */
  --alert-low: #10b981;
  --alert-medium: #f59e0b;
  --alert-high: #ef4444;
  --alert-critical: #dc2626;

  /* Data visualization colors */
  --chart-primary: #3b82f6;
  --chart-secondary: #10b981;
  --chart-tertiary: #f59e0b;
  --chart-quaternary: #ef4444;
  --chart-quinary: #8b5cf6;
}

/* Enhanced typography classes */
.text-display-large {
  font-size: var(--text-6xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-bold);
  letter-spacing: -0.025em;
}

.text-display-medium {
  font-size: var(--text-5xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-bold);
  letter-spacing: -0.025em;
}

.text-display-small {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
  font-weight: var(--font-semibold);
  letter-spacing: -0.025em;
}

.text-headline-large {
  font-size: var(--text-3xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-semibold);
}

.text-headline-medium {
  font-size: var(--text-2xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-semibold);
}

.text-headline-small {
  font-size: var(--text-xl);
  line-height: var(--leading-snug);
  font-weight: var(--font-medium);
}

.text-body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
}

.text-body-medium {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
}

.text-body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
}

.text-label-large {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
}

.text-label-medium {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
}

.text-label-small {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: var(--font-medium);
}

/* Enhanced spacing utilities */
.section-spacing {
  padding: var(--space-16) 0;
}

.container-spacing {
  padding: 0 var(--space-6);
}

.card-spacing {
  padding: var(--space-6);
}

.element-spacing {
  margin-bottom: var(--space-4);
}

/* Visual hierarchy helpers */
.visual-hierarchy-1 {
  position: relative;
  z-index: 10;
}

.visual-hierarchy-2 {
  position: relative;
  z-index: 5;
}

.visual-hierarchy-3 {
  position: relative;
  z-index: 1;
}

/* Enhanced focus states */
.focus-enhanced:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
}

/* Weather-specific styling */
.weather-card {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease-in-out;
}

.weather-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.weather-alert-low {
  border-left: 4px solid var(--alert-low);
  background-color: rgb(16 185 129 / 0.05);
}

.weather-alert-medium {
  border-left: 4px solid var(--alert-medium);
  background-color: rgb(245 158 11 / 0.05);
}

.weather-alert-high {
  border-left: 4px solid var(--alert-high);
  background-color: rgb(239 68 68 / 0.05);
}

.weather-alert-critical {
  border-left: 4px solid var(--alert-critical);
  background-color: rgb(220 38 38 / 0.05);
}

/* Data visualization enhancements */
.chart-container {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
}

/* Enhanced animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.2s ease-out;
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .container-spacing {
    padding: 0 var(--space-4);
  }
  
  .section-spacing {
    padding: var(--space-12) 0;
  }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .weather-card {
    border: 2px solid hsl(var(--border));
  }
  
  .chart-container {
    border: 1px solid hsl(var(--border));
  }
}
