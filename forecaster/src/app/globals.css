@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }

  /* Focus styles for better accessibility */
  .keyboard-user *:focus {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* High contrast mode support */
  .high-contrast {
    filter: contrast(150%);
  }

  /* OpenLayers attribution fix - make it subtle and properly positioned */
  .ol-attribution {
    position: absolute !important;
    bottom: 8px !important;
    right: 8px !important;
    background: rgba(255, 255, 255, 0.85) !important;
    color: #666 !important;
    font-size: 10px !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    max-width: 200px !important;
    z-index: 100 !important;
    transition: all 0.2s ease !important;
    opacity: 0.7 !important;
  }

  .ol-attribution:hover {
    opacity: 1 !important;
    background: rgba(255, 255, 255, 0.95) !important;
  }

  .ol-attribution.ol-collapsed {
    min-width: auto !important;
    width: auto !important;
  }

  .ol-attribution ul {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    font-size: 10px !important;
  }

  .ol-attribution li {
    display: inline !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 10px !important;
  }

  .ol-attribution a {
    color: #0066cc !important;
    text-decoration: none !important;
    font-size: 10px !important;
  }

  .ol-attribution a:hover {
    text-decoration: underline !important;
  }

  .ol-attribution button {
    background: transparent !important;
    border: none !important;
    color: #666 !important;
    cursor: pointer !important;
    padding: 0 2px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    width: 14px !important;
    height: 14px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .ol-attribution button:hover {
    color: #333 !important;
  }

  /* Collapsed state - show only the "i" button */
  .ol-attribution.ol-collapsed ul {
    display: none !important;
  }

  .ol-attribution.ol-collapsed {
    padding: 1px !important;
    background: rgba(255, 255, 255, 0.7) !important;
  }

  .ol-attribution.ol-collapsed button {
    width: 16px !important;
    height: 16px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
  }

  /* Dark mode attribution */
  .dark .ol-attribution {
    background: rgba(0, 0, 0, 0.7) !important;
    color: #ccc !important;
  }

  .dark .ol-attribution:hover {
    background: rgba(0, 0, 0, 0.85) !important;
  }

  .dark .ol-attribution button {
    color: #ccc !important;
  }

  .dark .ol-attribution button:hover {
    color: #fff !important;
  }

  .dark .ol-attribution a {
    color: #60a5fa !important;
  }

  .dark .ol-attribution.ol-collapsed {
    background: rgba(0, 0, 0, 0.6) !important;
  }

  .dark .ol-attribution.ol-collapsed button {
    background: rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ccc !important;
  }

  /* OpenLayers overlay container styling */
  .ol-overlay-container {
    pointer-events: none !important;
    z-index: 1000 !important;
  }

  .ol-overlay-container > div {
    pointer-events: auto !important;
    -webkit-backdrop-filter: blur(8px) !important;
    backdrop-filter: blur(8px) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 8px !important;
    transition: all 0.2s ease-in-out !important;
  }

  /* Dark mode overlay styling */
  .dark .ol-overlay-container > div {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  /* Popup arrow styling */
  .ol-overlay-container > div::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 0 !important;
    height: 0 !important;
    border-left: 8px solid transparent !important;
    border-right: 8px solid transparent !important;
    border-top: 8px solid rgba(255, 255, 255, 0.95) !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
  }

  .dark .ol-overlay-container > div::after {
    border-top-color: rgba(0, 0, 0, 0.9) !important;
  }

  /* Smooth animations for popup */
  .ol-overlay-container[style*="display: block"] > div {
    animation: popupFadeIn 0.2s ease-out !important;
  }

  @keyframes popupFadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -100%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -100%) scale(1);
    }
  }

  /* Ensure popup content is properly styled */
  .ol-overlay-container .space-y-2 > * + * {
    margin-top: 0.5rem !important;
  }

  .ol-overlay-container .grid {
    display: grid !important;
  }

  .ol-overlay-container .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .ol-overlay-container .gap-2 {
    gap: 0.5rem !important;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Font size variants */
  .font-size-small {
    font-size: 14px;
  }

  .font-size-medium {
    font-size: 16px;
  }

  .font-size-large {
    font-size: 18px;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Loading animation */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    background-size: 200% 100%;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Glass effect */
  .glass {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
