"use client";

import { cn } from "@/lib/utils";
import { getWindDirectionRotation } from "@/lib/format";

interface WindArrowProps {
  degrees: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function WindArrow({ degrees, className, size = 'md' }: WindArrowProps) {
  const rotation = getWindDirectionRotation(degrees);
  
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4', 
    lg: 'w-6 h-6'
  };

  return (
    <div
      className={cn(
        "inline-flex items-center justify-center",
        sizeClasses[size],
        className
      )}
      style={{
        transform: `rotate(${rotation}deg)`,
      }}
      aria-label={`Wind direction: ${Math.round(degrees)}°`}
    >
      {/* CSS-based arrow using borders - more pointy design */}
      <div className="relative">
        {/* Arrow shaft - thinner and longer */}
        <div className="w-0.5 h-2.5 bg-current absolute left-1/2 top-1 transform -translate-x-1/2" />

        {/* Arrow head - sharper and more pointy */}
        <div
          className="absolute top-0 left-1/2 transform -translate-x-1/2"
          style={{
            width: 0,
            height: 0,
            borderLeft: '4px solid transparent',
            borderRight: '4px solid transparent',
            borderBottom: '6px solid currentColor',
          }}
        />
      </div>
    </div>
  );
}

// Alternative SVG-based arrow (more precise)
export function WindArrowSVG({ degrees, className, size = 'md' }: WindArrowProps) {
  const rotation = getWindDirectionRotation(degrees);
  
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-6 h-6'
  };

  return (
    <svg
      className={cn(sizeClasses[size], className)}
      viewBox="0 0 16 16"
      fill="currentColor"
      style={{
        transform: `rotate(${rotation}deg)`,
      }}
      aria-label={`Wind direction: ${Math.round(degrees)}°`}
    >
      {/* Arrow pointing up (will be rotated) - more pointy design */}
      <path d="M8 1 L13 7 L10.5 7 L10.5 14 L5.5 14 L5.5 7 L3 7 Z" />
    </svg>
  );
}

// Simple ASCII-style arrow (fallback)
export function WindArrowText({ degrees, className }: Omit<WindArrowProps, 'size'>) {
  // Normalize degrees to 0-360 range
  const normalizedDegrees = ((degrees % 360) + 360) % 360;
  
  // Convert to 8 main directions
  const index = Math.round(normalizedDegrees / 45) % 8;
  
  // Use simple ASCII-style arrows that won't render as emojis
  const arrows = [
    '↓', // Down (S)
    '↙', // Down-Left (SW) 
    '←', // Left (W)
    '↖', // Up-Left (NW)
    '↑', // Up (N)
    '↗', // Up-Right (NE)
    '→', // Right (E)
    '↘'  // Down-Right (SE)
  ];

  return (
    <span className={cn("inline-block font-mono", className)}>
      {arrows[index]}
    </span>
  );
}
